import { supabase } from '@indie-points/lib';

import {
  CacheEntry,
  RequestOptions,
  RetryConfig,
  ServiceResponse,
} from './types';

/**
 * Base service class providing common functionality for all services
 * Includes caching, retry logic, error handling, and request deduplication
 */
export abstract class BaseService {
  protected cache: Map<string, CacheEntry<any>> = new Map();
  protected pendingRequests: Map<string, Promise<any>> = new Map();
  protected defaultRetryConfig: RetryConfig = {
    attempts: 3,
    backoff: 1000,
    maxBackoff: 10000,
  };

  /**
   * Make a request with caching, retry logic, and error handling
   */
  protected async makeRequest<T>(
    key: string,
    requestFn: () => Promise<T>,
    options: RequestOptions = {}
  ): Promise<ServiceResponse<T>> {
    const {
      useCache = true,
      cacheTtl = 300000, // 5 minutes default
      retryConfig = {},
    } = options;

    // Check cache first
    if (useCache) {
      const cached = this.getCachedData<T>(key, cacheTtl);
      if (cached) {
        return { data: cached, error: null };
      }
    }

    // Check for pending request to prevent duplicates
    const pendingKey = `pending_${key}`;
    if (this.pendingRequests.has(pendingKey)) {
      try {
        const result = await this.pendingRequests.get(pendingKey);
        return { data: result, error: null };
      } catch (error) {
        return {
          data: null,
          error: error instanceof Error ? error.message : 'Request failed',
        };
      }
    }

    // Create new request with retry logic
    const finalRetryConfig = { ...this.defaultRetryConfig, ...retryConfig };
    const requestPromise = this.executeWithRetry(requestFn, finalRetryConfig);

    // Store pending request
    this.pendingRequests.set(pendingKey, requestPromise);

    try {
      const result = await requestPromise;

      // Cache successful result
      if (useCache && result) {
        this.setCachedData(key, result, cacheTtl);
      }

      return { data: result, error: null };
    } catch (error) {
      this.logError('makeRequest', error, { key, options });
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Request failed',
      };
    } finally {
      // Clean up pending request
      this.pendingRequests.delete(pendingKey);
    }
  }

  /**
   * Execute a function with retry logic and exponential backoff
   */
  private async executeWithRetry<T>(
    fn: () => Promise<T>,
    config: RetryConfig
  ): Promise<T> {
    let lastError: Error;
    let delay = config.backoff;

    for (let attempt = 1; attempt <= config.attempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        // Don't retry on the last attempt
        if (attempt === config.attempts) {
          break;
        }

        // Wait before retrying with exponential backoff
        await this.sleep(delay);
        delay = Math.min(delay * 2, config.maxBackoff || 10000);
      }
    }

    throw lastError!;
  }

  /**
   * Get cached data if it exists and hasn't expired
   */
  protected getCachedData<T>(key: string, ttl: number): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const now = Date.now();
    if (now - entry.timestamp > ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Set data in cache with timestamp
   */
  protected setCachedData<T>(key: string, data: T, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  /**
   * Clear cache for a specific key or all cache
   */
  protected clearCache(key?: string): void {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Make a Supabase RPC call with error handling
   */
  protected async makeSupabaseRpc<T>(
    functionName: string,
    params: Record<string, any> = {},
    options: RequestOptions = {}
  ): Promise<ServiceResponse<T>> {
    const cacheKey = `rpc_${functionName}_${JSON.stringify(params)}`;

    return this.makeRequest(
      cacheKey,
      async () => {
        const { data, error } = await supabase.rpc(functionName, params);

        if (error) {
          throw new Error(error.message || `RPC call ${functionName} failed`);
        }

        return data;
      },
      options
    );
  }

  /**
   * Log errors with context
   */
  protected logError(
    method: string,
    error: unknown,
    context?: Record<string, any>
  ): void {
    console.error(`[${this.constructor.name}] ${method}:`, {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      context,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Sleep utility for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Transform data between external and internal formats
   * Override in subclasses as needed
   */
  protected transformResponse<T>(data: any): T {
    return data;
  }

  /**
   * Validate data before processing
   * Override in subclasses as needed
   */
  protected validateData(data: any): boolean {
    return data != null;
  }
}
