import { useCallback } from 'react';

import { useCustomerPointsSummary } from '../atomic';

interface UseHomeScreenReturn {
  pointsData: ReturnType<typeof useCustomerPointsSummary>['data'];
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

/**
 * Composite hook for the Home screen
 * Combines customer points summary data for the home screen
 * @param customerId - The customer's UUID
 * @returns Combined data and functionality for the home screen
 */
export function useHomeScreen(customerId?: string): UseHomeScreenReturn {
  const pointsSummary = useCustomerPointsSummary(customerId);

  const refresh = useCallback(async () => {
    await pointsSummary.refetch();
  }, [pointsSummary]);

  return {
    pointsData: pointsSummary.data,
    loading: pointsSummary.loading,
    error: pointsSummary.error,
    refresh,
  };
}
