import { useCallback } from 'react';

import {
  useBusinessRewardEligibility,
  useCustomerTransactionHistoryForBusiness,
} from '../atomic';

interface UseBusinessHistoryScreenReturn {
  transactionData: ReturnType<
    typeof useCustomerTransactionHistoryForBusiness
  >['data'];
  rewardData: ReturnType<typeof useBusinessRewardEligibility>['data'];
  loading: boolean;
  error: string | null;
  hasMoreTransactions: boolean;
  loadingMoreTransactions: boolean;
  refresh: () => Promise<void>;
  loadMoreTransactions: () => Promise<void>;
}

/**
 * Composite hook for the Business History screen
 * Combines transaction history for a specific business and reward eligibility
 * @param customerId - The customer's UUID
 * @param businessId - The business's UUID
 * @returns Combined data and functionality for the business history screen
 */
export function useBusinessHistoryScreen(
  customerId?: string,
  businessId?: string
): UseBusinessHistoryScreenReturn {
  const transactionHistory = useCustomerTransactionHistoryForBusiness(
    customerId,
    businessId
  );
  const rewardEligibility = useBusinessRewardEligibility(
    customerId,
    businessId
  );

  const refresh = useCallback(async () => {
    await Promise.all([
      transactionHistory.refetch(),
      rewardEligibility.refetch(),
    ]);
  }, [transactionHistory.refetch, rewardEligibility.refetch]);

  const loading = transactionHistory.loading || rewardEligibility.loading;
  const error = transactionHistory.error || rewardEligibility.error;

  return {
    transactionData: transactionHistory.data,
    rewardData: rewardEligibility.data,
    loading,
    error,
    hasMoreTransactions: transactionHistory.hasMore,
    loadingMoreTransactions: transactionHistory.loadingMore,
    refresh,
    loadMoreTransactions: transactionHistory.loadMore,
  };
}
