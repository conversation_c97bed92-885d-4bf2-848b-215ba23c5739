import { useCallback } from 'react';

import {
  useCustomerBusinessSummaries,
  useCustomerTransactionHistory,
} from '../atomic';

interface UseHistoryScreenReturn {
  transactionData: ReturnType<typeof useCustomerTransactionHistory>['data'];
  businessData: ReturnType<typeof useCustomerBusinessSummaries>['data'];
  loading: boolean;
  error: string | null;
  hasMoreTransactions: boolean;
  loadingMoreTransactions: boolean;
  refresh: () => Promise<void>;
  loadMoreTransactions: () => Promise<void>;
}

/**
 * Composite hook for the History screen
 * Combines transaction history and business summaries for the history screen
 * @param customerId - The customer's UUID
 * @returns Combined data and functionality for the history screen
 */
export function useHistoryScreen(customerId?: string): UseHistoryScreenReturn {
  const transactionHistory = useCustomerTransactionHistory(customerId);
  const businessSummaries = useCustomerBusinessSummaries(customerId);

  const refresh = useCallback(async () => {
    await Promise.all([
      transactionHistory.refetch(),
      businessSummaries.refetch(),
    ]);
  }, [transactionHistory, businessSummaries]);

  const loading = transactionHistory.loading || businessSummaries.loading;
  const error = transactionHistory.error || businessSummaries.error;

  return {
    transactionData: transactionHistory.data,
    businessData: businessSummaries.data,
    loading,
    error,
    hasMoreTransactions: transactionHistory.hasMore,
    loadingMoreTransactions: transactionHistory.loadingMore,
    refresh,
    loadMoreTransactions: transactionHistory.loadMore,
  };
}
