import { useCallback } from 'react';

import { useCreateVisitTransaction } from '../atomic';

interface UseScanScreenReturn {
  visitData: ReturnType<typeof useCreateVisitTransaction>['data'];
  loading: boolean;
  error: string | null;
  createVisit: (
    customerId: string,
    businessId: string,
    businessName: string,
    qrToken: string
  ) => Promise<void>;
  reset: () => void;
}

/**
 * Composite hook for the Scan screen
 * Manages visit transaction creation for QR code scanning
 * @returns Visit transaction functionality for the scan screen
 */
export function useScanScreen(): UseScanScreenReturn {
  const visitTransaction = useCreateVisitTransaction();

  const createVisit = useCallback(
    async (
      customerId: string,
      businessId: string,
      businessName: string,
      qrToken: string
    ) => {
      await visitTransaction.createVisit(
        customerId,
        businessId,
        businessName,
        qrToken
      );
    },
    [visitTransaction.createVisit]
  );

  const reset = useCallback(() => {
    visitTransaction.reset();
  }, [visitTransaction.reset]);

  return {
    visitData: visitTransaction.data,
    loading: visitTransaction.loading,
    error: visitTransaction.error,
    createVisit,
    reset,
  };
}
