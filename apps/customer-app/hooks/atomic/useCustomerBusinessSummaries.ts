import { useCallback, useEffect, useState } from 'react';

import {
  CustomerBusinessSummary,
  RequestOptions,
  TransactionsService,
} from '../../services';

interface UseCustomerBusinessSummariesState {
  data: CustomerBusinessSummary[];
  loading: boolean;
  error: string | null;
}

interface UseCustomerBusinessSummariesReturn
  extends UseCustomerBusinessSummariesState {
  refetch: () => Promise<void>;
}

/**
 * Atomic hook for fetching customer business summaries
 * @param customerId - The customer's UUID
 * @param options - Request options for caching and retry behavior
 * @returns Customer business summaries data with loading and error states
 */
export function useCustomerBusinessSummaries(
  customerId?: string,
  options: RequestOptions = {}
): UseCustomerBusinessSummariesReturn {
  const [state, setState] = useState<UseCustomerBusinessSummariesState>({
    data: [],
    loading: false,
    error: null,
  });

  const fetchData = useCallback(async () => {
    if (!customerId) {
      setState(prev => ({ ...prev, loading: false }));
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await TransactionsService.getCustomerBusinessSummaries(
        customerId,
        options
      );

      setState({
        data: result.data || [],
        loading: false,
        error: result.error,
      });
    } catch (error) {
      setState({
        data: [],
        loading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    }
  }, [customerId, options]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    ...state,
    refetch: fetchData,
  };
}
