import { useCallback, useState } from 'react';

import {
  RequestOptions,
  TransactionsService,
  VisitTransactionResponse,
} from '../../services';

interface UseCreateVisitTransactionState {
  data: VisitTransactionResponse | null;
  loading: boolean;
  error: string | null;
}

interface UseCreateVisitTransactionReturn
  extends UseCreateVisitTransactionState {
  createVisit: (
    customerId: string,
    businessId: string,
    businessName: string,
    qrToken: string
  ) => Promise<void>;
  reset: () => void;
}

/**
 * Atomic hook for creating visit transactions
 * @param options - Request options for caching and retry behavior
 * @returns Visit transaction creation functionality with loading and error states
 */
export function useCreateVisitTransaction(
  options: RequestOptions = {}
): UseCreateVisitTransactionReturn {
  const [state, setState] = useState<UseCreateVisitTransactionState>({
    data: null,
    loading: false,
    error: null,
  });

  const createVisit = useCallback(
    async (
      customerId: string,
      businessId: string,
      businessName: string,
      qrToken: string
    ) => {
      setState(prev => ({ ...prev, loading: true, error: null }));

      try {
        const result = await TransactionsService.createVisitTransaction(
          customerId,
          businessId,
          businessName,
          qrToken,
          options
        );

        setState({
          data: result.data,
          loading: false,
          error: result.error,
        });
      } catch (error) {
        setState({
          data: null,
          loading: false,
          error:
            error instanceof Error
              ? error.message
              : 'An unexpected error occurred',
        });
      }
    },
    [options]
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  return {
    ...state,
    createVisit,
    reset,
  };
}
