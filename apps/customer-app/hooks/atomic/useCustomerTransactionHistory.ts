import { useCallback, useEffect, useState } from 'react';

import {
  CustomerTransaction,
  RequestOptions,
  TransactionsService,
} from '../../services';

interface UseCustomerTransactionHistoryState {
  data: CustomerTransaction[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
}

interface UseCustomerTransactionHistoryReturn
  extends UseCustomerTransactionHistoryState {
  refetch: () => Promise<void>;
  loadMore: () => Promise<void>;
  loadingMore: boolean;
}

/**
 * Atomic hook for fetching customer transaction history with pagination
 * @param customerId - The customer's UUID
 * @param pageSize - Number of transactions per page
 * @param options - Request options for caching and retry behavior
 * @returns Customer transaction history data with loading and error states
 */
export function useCustomerTransactionHistory(
  customerId?: string,
  pageSize: number = 100,
  options: RequestOptions = {}
): UseCustomerTransactionHistoryReturn {
  const [state, setState] = useState<UseCustomerTransactionHistoryState>({
    data: [],
    loading: false,
    error: null,
    hasMore: true,
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);

  const fetchData = useCallback(
    async (page: number = 1, isRefresh = false) => {
      if (!customerId) {
        setState(prev => ({ ...prev, loading: false }));
        return;
      }

      if (page === 1) {
        if (!isRefresh)
          setState(prev => ({ ...prev, loading: true, error: null }));
      } else {
        setLoadingMore(true);
      }

      try {
        const result = await TransactionsService.getCustomerTransactionHistory(
          customerId,
          page,
          pageSize,
          options
        );

        if (result.error) {
          setState(prev => ({
            ...prev,
            loading: false,
            error: result.error,
          }));
          if (page > 1) setLoadingMore(false);
          return;
        }

        const newTransactions = result.data || [];
        const hasMore = newTransactions.length === pageSize;

        setState(prev => ({
          data:
            page === 1 ? newTransactions : [...prev.data, ...newTransactions],
          loading: false,
          error: null,
          hasMore,
        }));

        if (page > 1) setLoadingMore(false);
      } catch (error) {
        setState(prev => ({
          ...prev,
          loading: false,
          error:
            error instanceof Error
              ? error.message
              : 'An unexpected error occurred',
        }));
        if (page > 1) setLoadingMore(false);
      }
    },
    [customerId, pageSize, options]
  );

  const refetch = useCallback(async () => {
    setCurrentPage(1);
    await fetchData(1, true);
  }, [fetchData]);

  const loadMore = useCallback(async () => {
    if (!state.hasMore || loadingMore) return;
    const nextPage = currentPage + 1;
    setCurrentPage(nextPage);
    await fetchData(nextPage);
  }, [state.hasMore, loadingMore, currentPage, fetchData]);

  useEffect(() => {
    fetchData(1);
  }, [fetchData]);

  return {
    ...state,
    refetch,
    loadMore,
    loadingMore,
  };
}
