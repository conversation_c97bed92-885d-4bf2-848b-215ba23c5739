import { useCallback, useEffect, useState } from 'react';

import {
  CustomerPointsSummary,
  PointsService,
  RequestOptions,
} from '../../services';

interface UseCustomerPointsSummaryState {
  data: CustomerPointsSummary | null;
  loading: boolean;
  error: string | null;
}

interface UseCustomerPointsSummaryReturn extends UseCustomerPointsSummaryState {
  refetch: () => Promise<void>;
}

/**
 * Atomic hook for fetching customer points summary
 * @param customerId - The customer's UUID
 * @param options - Request options for caching and retry behavior
 * @returns Customer points summary data with loading and error states
 */
export function useCustomerPointsSummary(
  customerId?: string,
  options: RequestOptions = {}
): UseCustomerPointsSummaryReturn {
  const [state, setState] = useState<UseCustomerPointsSummaryState>({
    data: null,
    loading: false,
    error: null,
  });

  const fetchData = useCallback(async () => {
    if (!customerId) {
      setState(prev => ({ ...prev, loading: false }));
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await PointsService.getCustomerPointsSummary(
        customerId,
        options
      );

      setState({
        data: result.data,
        loading: false,
        error: result.error,
      });
    } catch (error) {
      setState({
        data: null,
        loading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    }
  }, [customerId, options]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    ...state,
    refetch: fetchData,
  };
}
