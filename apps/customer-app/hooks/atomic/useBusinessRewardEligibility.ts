import { useCallback, useEffect, useState } from 'react';

import {
  CustomerRewardEligibility,
  RequestOptions,
  TransactionsService,
} from '../../services';

interface UseBusinessRewardEligibilityState {
  data: CustomerRewardEligibility[];
  loading: boolean;
  error: string | null;
}

interface UseBusinessRewardEligibilityReturn
  extends UseBusinessRewardEligibilityState {
  refetch: () => Promise<void>;
}

/**
 * Atomic hook for fetching business reward eligibility for a customer
 * @param customerId - The customer's UUID
 * @param businessId - The business's UUID
 * @param options - Request options for caching and retry behavior
 * @returns Business reward eligibility data with loading and error states
 */
export function useBusinessRewardEligibility(
  customerId?: string,
  businessId?: string,
  options: RequestOptions = {}
): UseBusinessRewardEligibilityReturn {
  const [state, setState] = useState<UseBusinessRewardEligibilityState>({
    data: [],
    loading: false,
    error: null,
  });

  const fetchData = useCallback(async () => {
    if (!customerId || !businessId) {
      setState(prev => ({ ...prev, loading: false }));
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await TransactionsService.getBusinessRewardEligibility(
        customerId,
        businessId,
        options
      );

      setState({
        data: result.data || [],
        loading: false,
        error: result.error,
      });
    } catch (error) {
      setState({
        data: [],
        loading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    }
  }, [customerId, businessId, options]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    ...state,
    refetch: fetchData,
  };
}
