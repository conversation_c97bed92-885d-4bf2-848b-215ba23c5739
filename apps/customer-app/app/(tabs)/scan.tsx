import { Grad<PERSON>Bar } from '@indie-points/auth';
import { useAuth } from '@indie-points/contexts';
import { Alert, AlertText } from '@indie-points/ui-alert';
import { Box } from '@indie-points/ui-box';
import { Heading } from '@indie-points/ui-heading';
import { VStack } from '@indie-points/ui-vstack';
import {
  BarcodeScanningResult,
  CameraType,
  useCameraPermissions,
} from 'expo-camera';
import React, { useEffect, useState } from 'react';
import { ScrollView } from 'react-native';

import { CameraScanner, InstructionSteps } from '../../components';
import { useScanScreen } from '../../hooks';

// Types for QR code data
interface QRCodeData {
  businessId: string;
  businessName: string;
  qrToken: string;
}

export default function Scan() {
  const { user } = useAuth();
  const [facing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [scannedData, setScannedData] = useState<QRCodeData | null>(null);
  const [scanCompleted, setScanCompleted] = useState(false);

  const { visitData, loading, error, createVisit, reset } = useScanScreen();

  useEffect(() => {
    if (permission && !permission.granted) {
      requestPermission();
    }
  }, [permission, requestPermission]);

  // Parse QR code data
  const parseQRCodeData = (data: string): QRCodeData | null => {
    try {
      const parsed = JSON.parse(data);
      if (parsed.businessId && parsed.businessName && parsed.token) {
        return {
          businessId: parsed.businessId,
          businessName: parsed.businessName,
          qrToken: parsed.token,
        };
      }
      return null;
    } catch (error) {
      console.error('Error parsing QR code data:', error);
      return null;
    }
  };

  // Handle QR code scan
  const handleBarCodeScanned = async (result: BarcodeScanningResult) => {
    if (loading || !user?.id || scanCompleted) return;

    const qrData = parseQRCodeData(result.data);
    if (!qrData) {
      setErrorMessage(
        'Invalid QR code format. Please scan a valid business QR code.'
      );
      setScanCompleted(true);
      return;
    }

    setErrorMessage(null);
    setSuccessMessage(null);
    setScannedData(qrData);

    try {
      await createVisit(
        user.id,
        qrData.businessId,
        qrData.businessName,
        qrData.qrToken
      );

      if (error) {
        setErrorMessage(error);
      } else if (visitData) {
        if (visitData.alreadyExists) {
          setSuccessMessage(`You have already visited ${qrData.businessName}!`);
        } else {
          setSuccessMessage(
            `Success! You earned 1 point at ${qrData.businessName}!`
          );
        }
      }
    } catch (err) {
      console.error('Error processing visit transaction:', err);
      setErrorMessage('Failed to process visit. Please try again.');
    } finally {
      setScanCompleted(true);
    }
  };

  // Handle scan again
  const handleScanAgain = () => {
    setScanCompleted(false);
    setSuccessMessage(null);
    setErrorMessage(null);
    setScannedData(null);
    reset();
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            Scan
          </Heading>

          {/* Colored divider line */}
          <GradientBar />
        </VStack>

        <Box className='px-6 pb-8'>
          <VStack space='xl'>
            {/* Feedback Messages */}
            {successMessage && (
              <Alert action='success' variant='solid'>
                <AlertText>{successMessage}</AlertText>
              </Alert>
            )}
            {errorMessage && (
              <Alert action='error' variant='solid'>
                <AlertText>{errorMessage}</AlertText>
              </Alert>
            )}

            {/* Camera section */}
            <VStack space='lg' className='items-center'>
              <Heading size='xl' className='text-typography-900 font-semibold'>
                Scan a business QR code
              </Heading>
              <CameraScanner
                permission={permission}
                facing={facing}
                isProcessing={loading}
                scanCompleted={scanCompleted}
                scannedBusinessName={scannedData?.businessName}
                onBarCodeScanned={handleBarCodeScanned}
                onScanAgain={handleScanAgain}
              />
            </VStack>

            <InstructionSteps
              title='How to claim a bonus point'
              steps={[
                {
                  number: 1,
                  title: 'Visit a participating business',
                  description:
                    'Look for the Indie Points logo at local businesses',
                  color: 'bg-primary-500',
                },
                {
                  number: 2,
                  title: 'Open the scan tab',
                  description:
                    'Use your phone to scan the QR code of the business',
                  color: 'bg-secondary-500',
                },
                {
                  number: 3,
                  title: 'Claim your bonus point',
                  description:
                    'After scanning, you will receive a bonus point for visiting',
                  color: 'bg-error-500',
                },
              ]}
            />
          </VStack>
        </Box>
      </ScrollView>
    </Box>
  );
}
