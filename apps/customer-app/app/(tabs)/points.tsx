import { GradientBar } from '@indie-points/auth';
import { useAuth } from '@indie-points/contexts';
import { Box } from '@indie-points/ui-box';
import { Heading } from '@indie-points/ui-heading';
import { VStack } from '@indie-points/ui-vstack';
import React, { useCallback, useState } from 'react';
import { ScrollView } from 'react-native';

import { InstructionSteps, QRCodeDisplay } from '../../components';

export default function Points() {
  const { user } = useAuth();

  // Helper to generate QR code payload
  const generateQrPayload = useCallback(() => {
    const now = Date.now();
    return JSON.stringify({
      expiry: now + 60 * 60 * 1000,
      issuedAt: now,
      userId: user?.id ?? null,
    });
  }, [user?.id]);

  const [qrValue, setQrValue] = useState(generateQrPayload);
  const [qrWidth, setQrWidth] = useState(0);

  const handleGenerateNewQr = useCallback(() => {
    setQrValue(generateQrPayload());
  }, [generateQrPayload]);

  const instructionSteps = [
    {
      number: 1,
      title: 'Visit a participating business',
      description: 'Look for the Indie Points logo at local businesses',
      color: 'bg-primary-500',
    },
    {
      number: 2,
      title: 'Show your QR code',
      description:
        'Let the business scan your unique QR code before or after purchase',
      color: 'bg-secondary-500',
    },
    {
      number: 3,
      title: 'Earn points automatically',
      description:
        'Points are added to your account instantly after each visit',
      color: 'bg-error-500',
    },
  ];

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            Points
          </Heading>

          {/* Colored divider line */}
          <GradientBar />
        </VStack>

        <Box className='px-6 pb-8'>
          <VStack space='xl'>
            <QRCodeDisplay
              qrValue={qrValue}
              qrWidth={qrWidth}
              onGenerateNew={handleGenerateNewQr}
              onLayout={event => {
                const width = event.nativeEvent.layout.width;
                setQrWidth(width);
              }}
            />

            <InstructionSteps
              title='How to earn points'
              steps={instructionSteps}
            />
          </VStack>
        </Box>
      </ScrollView>
    </Box>
  );
}
