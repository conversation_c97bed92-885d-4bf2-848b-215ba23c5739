import { useAuth } from '@indie-points/contexts';
import { Box } from '@indie-points/ui-box';
import { RefreshControl } from '@indie-points/ui-refresh-control';
import { Spinner } from '@indie-points/ui-spinner';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React, { useState } from 'react';
import { ScrollView } from 'react-native';

import {
  InstructionSteps,
  PointsSummaryCard,
  WelcomeHeader,
} from '../../components';
import { useHomeScreen } from '../../hooks';

export default function Home() {
  const { user } = useAuth();
  const { pointsData, loading, error, refresh } = useHomeScreen(user?.id);
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    await refresh();
    setRefreshing(false);
  };

  const instructionSteps = [
    {
      number: 1,
      title: 'Visit a participating business',
      description: 'Look for the Indie Points logo at local businesses',
      color: 'bg-primary-500',
    },
    {
      number: 2,
      title: 'Show your QR code',
      description:
        'Let the business scan your unique QR code before or after purchase',
      color: 'bg-secondary-500',
    },
    {
      number: 3,
      title: 'Earn points automatically',
      description:
        'Points are added to your account instantly after each visit',
      color: 'bg-error-500',
    },
  ];

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        <WelcomeHeader userName={user?.user_metadata?.full_name} />

        <Box className='px-6 pb-8'>
          <VStack space='xl'>
            {loading && !pointsData ? (
              <Box className='bg-white rounded-2xl border-4 border-typography-900 p-6 shadow-lg'>
                <VStack space='lg' className='items-center'>
                  <Spinner size='large' />
                  <Text size='md' className='text-typography-600'>
                    Loading your points...
                  </Text>
                </VStack>
              </Box>
            ) : error ? (
              <Box className='bg-white rounded-2xl border-4 border-typography-900 p-6 shadow-lg'>
                <VStack space='lg' className='items-center'>
                  <Text size='lg' className='text-error-600 font-semibold'>
                    Error loading points
                  </Text>
                  <Text size='md' className='text-typography-600 text-center'>
                    {error}
                  </Text>
                </VStack>
              </Box>
            ) : (
              <PointsSummaryCard data={pointsData} loading={loading} />
            )}

            <InstructionSteps
              title='How to earn points'
              steps={instructionSteps}
            />
          </VStack>
        </Box>
      </ScrollView>
    </Box>
  );
}
