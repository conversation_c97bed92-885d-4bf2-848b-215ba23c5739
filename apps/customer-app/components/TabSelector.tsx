import { HStack } from '@indie-points/ui-hstack';
import { Pressable } from '@indie-points/ui-pressable';
import { Text } from '@indie-points/ui-text';
import * as Haptics from 'expo-haptics';
import React from 'react';

interface Tab {
  key: string;
  label: string;
}

interface TabSelectorProps<T extends string> {
  tabs: Tab[];
  activeTab: T;
  onTabChange: (tab: T) => void;
}

export function TabSelector<T extends string>({
  tabs,
  activeTab,
  onTabChange,
}: TabSelectorProps<T>) {
  const handleTabPress = (tabKey: T) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onTabChange(tabKey);
  };

  return (
    <HStack className='bg-gray-100 rounded-xl p-1 border-2 border-typography-900'>
      {tabs.map(tab => (
        <Pressable
          key={tab.key}
          onPress={() => handleTabPress(tab.key as T)}
          className={`flex-1 py-3 px-4 rounded-lg ${
            activeTab === tab.key
              ? 'bg-white border-2 border-typography-900 shadow-sm'
              : 'bg-transparent'
          }`}
        >
          <Text
            size='md'
            className={`text-center font-semibold ${
              activeTab === tab.key
                ? 'text-typography-900'
                : 'text-typography-600'
            }`}
          >
            {tab.label}
          </Text>
        </Pressable>
      ))}
    </HStack>
  );
}
