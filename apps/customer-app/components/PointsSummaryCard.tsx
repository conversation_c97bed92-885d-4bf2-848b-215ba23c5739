import { Box } from '@indie-points/ui-box';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

import { CustomerPointsSummary } from '../services';

interface PointsSummaryCardProps {
  data: CustomerPointsSummary | null;
  loading?: boolean;
}

export function PointsSummaryCard({
  data,
  loading = false,
}: PointsSummaryCardProps) {
  if (loading) {
    return (
      <Box className='bg-white rounded-2xl border-4 border-typography-900 p-6 shadow-lg'>
        <VStack space='lg'>
          <Heading size='xl' className='text-typography-900 font-semibold'>
            Your points
          </Heading>
          <VStack space='md'>
            <Box className='h-8 bg-gray-200 rounded animate-pulse' />
            <Box className='h-6 bg-gray-200 rounded animate-pulse' />
            <Box className='h-6 bg-gray-200 rounded animate-pulse' />
          </VStack>
        </VStack>
      </Box>
    );
  }

  if (!data) {
    return (
      <Box className='bg-white rounded-2xl border-4 border-typography-900 p-6 shadow-lg'>
        <VStack space='lg'>
          <Heading size='xl' className='text-typography-900 font-semibold'>
            Your points
          </Heading>
          <Text size='md' className='text-typography-600'>
            No points data available
          </Text>
        </VStack>
      </Box>
    );
  }

  return (
    <Box className='bg-white rounded-2xl border-4 border-typography-900 p-6 shadow-lg'>
      <VStack space='lg'>
        <Heading size='xl' className='text-typography-900 font-semibold'>
          Your points
        </Heading>

        {/* Total Active Points */}
        <VStack space='sm'>
          <Text size='4xl' className='text-primary-500 font-bold text-center'>
            {data.totalActive}
          </Text>
          <Text size='lg' className='text-typography-600 text-center'>
            Active points
          </Text>
        </VStack>

        {/* Points Breakdown */}
        <VStack space='md'>
          <HStack className='justify-between items-center'>
            <Text size='md' className='text-typography-700'>
              Total earned:
            </Text>
            <Text size='md' className='text-typography-900 font-semibold'>
              {data.totalEarned}
            </Text>
          </HStack>
          <HStack className='justify-between items-center'>
            <Text size='md' className='text-typography-700'>
              Total redeemed:
            </Text>
            <Text size='md' className='text-typography-900 font-semibold'>
              {data.totalRedeemed}
            </Text>
          </HStack>
        </VStack>
      </VStack>
    </Box>
  );
}
