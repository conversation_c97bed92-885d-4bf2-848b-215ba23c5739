import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { Button, ButtonText } from '@indie-points/ui-button';
import { Spinner } from '@indie-points/ui-spinner';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import {
  BarcodeScanningResult,
  CameraType,
  CameraView,
  PermissionResponse,
} from 'expo-camera';
import React from 'react';

interface CameraScannerProps {
  permission: PermissionResponse | null;
  facing: CameraType;
  isProcessing: boolean;
  scanCompleted: boolean;
  scannedBusinessName?: string;
  onBarCodeScanned: (result: BarcodeScanningResult) => void;
  onScanAgain: () => void;
}

export function CameraScanner({
  permission,
  facing,
  isProcessing,
  scanCompleted,
  scannedBusinessName,
  onBarCodeScanned,
  onScanAgain,
}: CameraScannerProps) {
  const renderCameraContent = () => {
    if (!permission) {
      return (
        <Text size='md' className='text-white'>
          Requesting camera permission...
        </Text>
      );
    }

    if (!permission.granted) {
      return (
        <VStack space='md' className='items-center'>
          <FontAwesome name='camera' size={64} color='#fff' />
          <Text size='md' className='text-white text-center'>
            Camera access is required to scan business QR codes.
          </Text>
        </VStack>
      );
    }

    if (isProcessing) {
      return (
        <VStack space='md' className='items-center'>
          <Spinner size='large' color='#fff' />
          <Text size='md' className='text-white text-center'>
            Processing visit...
          </Text>
          {scannedBusinessName && (
            <Text size='sm' className='text-white text-center'>
              {scannedBusinessName}
            </Text>
          )}
        </VStack>
      );
    }

    if (scanCompleted) {
      return (
        <VStack space='lg' className='items-center justify-center flex-1'>
          <FontAwesome name='check-circle' size={64} color='#22c55e' />
          <Text size='lg' className='text-white text-center font-semibold'>
            Scan complete!
          </Text>
          <Button
            size='lg'
            action='primary'
            className='mt-4 bg-primary-500'
            onPress={onScanAgain}
          >
            <ButtonText>Scan again</ButtonText>
          </Button>
        </VStack>
      );
    }

    return (
      <CameraView
        style={{ width: '100%', aspectRatio: 1 }}
        facing={facing}
        ratio='1:1'
        barcodeScannerSettings={{
          barcodeTypes: ['qr'],
        }}
        onBarcodeScanned={onBarCodeScanned}
      />
    );
  };

  return (
    <Box className='w-full aspect-square bg-black rounded-2xl border-4 border-typography-900 items-center justify-center shadow-lg overflow-hidden'>
      {renderCameraContent()}
    </Box>
  );
}
