import { Box } from '@indie-points/ui-box';
import { But<PERSON>, ButtonText } from '@indie-points/ui-button';
import { Heading } from '@indie-points/ui-heading';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import * as Haptics from 'expo-haptics';
import React from 'react';
import QRCode from 'react-native-qrcode-svg';

interface QRCodeDisplayProps {
  qrValue: string;
  qrWidth: number;
  onGenerateNew: () => void;
  onLayout?: (event: any) => void;
}

export function QRCodeDisplay({
  qrValue,
  qrWidth,
  onGenerateNew,
  onLayout,
}: QRCodeDisplayProps) {
  const handleGenerateNew = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onGenerateNew();
  };

  return (
    <VStack space='lg' className='items-center'>
      <Heading size='xl' className='text-typography-900 font-semibold'>
        Your QR code
      </Heading>

      <Box
        className='bg-white rounded-2xl border-4 border-typography-900 p-6 shadow-lg items-center justify-center'
        onLayout={onLayout}
      >
        {qrWidth > 0 && (
          <QRCode
            value={qrValue}
            size={qrWidth - 48} // Account for padding
            backgroundColor='white'
            color='black'
          />
        )}
      </Box>

      <VStack space='md' className='items-center'>
        <Text size='md' className='text-typography-600 text-center'>
          Show this QR code to businesses to earn points
        </Text>

        <Button
          size='lg'
          action='secondary'
          variant='outline'
          className='border-2 border-typography-900'
          onPress={handleGenerateNew}
        >
          <ButtonText>Generate new QR code</ButtonText>
        </Button>
      </VStack>
    </VStack>
  );
}
