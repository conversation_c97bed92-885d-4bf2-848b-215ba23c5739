import { Box } from '@indie-points/ui-box';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Pressable } from '@indie-points/ui-pressable';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';
import { FlatList } from 'react-native';

import { CustomerTransaction } from '../services';

interface TransactionListProps {
  data: CustomerTransaction[];
  loading?: boolean;
  onTransactionPress?: (transaction: CustomerTransaction) => void;
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  ListFooterComponent?: React.ComponentType<any> | React.ReactElement | null;
}

interface TransactionItemProps {
  transaction: CustomerTransaction;
  onPress?: (transaction: CustomerTransaction) => void;
}

function TransactionItem({ transaction, onPress }: TransactionItemProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'purchase':
        return '💳';
      case 'redemption':
        return '🎁';
      case 'visit':
        return '📍';
      default:
        return '📝';
    }
  };

  return (
    <Pressable
      onPress={() => onPress?.(transaction)}
      className='bg-white rounded-xl border-2 border-typography-900 p-4 shadow-sm'
    >
      <HStack space='md' className='items-center'>
        <Text size='2xl'>{getTransactionIcon(transaction.type)}</Text>
        <VStack className='flex-1' space='xs'>
          <HStack className='justify-between items-center'>
            <Text
              size='md'
              className='text-typography-900 font-semibold flex-1'
            >
              {transaction.businessName}
            </Text>
            <Text size='sm' className='text-typography-600'>
              {formatDate(transaction.date)}
            </Text>
          </HStack>
          <HStack className='justify-between items-center'>
            <Text size='sm' className='text-typography-600 capitalize'>
              {transaction.type} • {transaction.businessCategory}
            </Text>
            <HStack space='sm'>
              {transaction.pointsEarned > 0 && (
                <Text size='sm' className='text-success-600 font-semibold'>
                  +{transaction.pointsEarned}
                </Text>
              )}
              {transaction.pointsRedeemed > 0 && (
                <Text size='sm' className='text-error-600 font-semibold'>
                  -{transaction.pointsRedeemed}
                </Text>
              )}
            </HStack>
          </HStack>
        </VStack>
      </HStack>
    </Pressable>
  );
}

export function TransactionList({
  data,
  loading = false,
  onTransactionPress,
  onEndReached,
  onEndReachedThreshold = 0.1,
  ListFooterComponent,
}: TransactionListProps) {
  if (loading && data.length === 0) {
    return (
      <VStack space='md'>
        {[...Array(3)].map((_, index) => (
          <Box
            key={index}
            className='bg-white rounded-xl border-2 border-gray-200 p-4 shadow-sm'
          >
            <HStack space='md' className='items-center'>
              <Box className='w-8 h-8 bg-gray-200 rounded animate-pulse' />
              <VStack className='flex-1' space='xs'>
                <Box className='h-4 bg-gray-200 rounded animate-pulse' />
                <Box className='h-3 bg-gray-200 rounded animate-pulse w-3/4' />
              </VStack>
            </HStack>
          </Box>
        ))}
      </VStack>
    );
  }

  if (data.length === 0) {
    return (
      <Box className='bg-white rounded-xl border-2 border-typography-900 p-6 shadow-sm'>
        <VStack space='md' className='items-center'>
          <Text size='4xl'>📝</Text>
          <Heading
            size='lg'
            className='text-typography-900 font-semibold text-center'
          >
            No transactions yet
          </Heading>
          <Text size='md' className='text-typography-600 text-center'>
            Your transaction history will appear here once you start earning and
            redeeming points.
          </Text>
        </VStack>
      </Box>
    );
  }

  return (
    <FlatList
      data={data}
      keyExtractor={item => item.id.toString()}
      renderItem={({ item }) => (
        <TransactionItem transaction={item} onPress={onTransactionPress} />
      )}
      ItemSeparatorComponent={() => <Box className='h-3' />}
      onEndReached={onEndReached}
      onEndReachedThreshold={onEndReachedThreshold}
      ListFooterComponent={ListFooterComponent}
      showsVerticalScrollIndicator={false}
    />
  );
}
