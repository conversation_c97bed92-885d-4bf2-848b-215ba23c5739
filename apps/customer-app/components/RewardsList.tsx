import { Box } from '@indie-points/ui-box';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';
import { FlatList } from 'react-native';

import { CustomerRewardEligibility } from '../services';

interface RewardsListProps {
  data: CustomerRewardEligibility[];
  loading?: boolean;
}

interface RewardItemProps {
  rewardEligibility: CustomerRewardEligibility;
}

function RewardItem({ rewardEligibility }: RewardItemProps) {
  const { reward, isEligible, customerPoints, pointsNeeded } =
    rewardEligibility;

  return (
    <Box
      className={`rounded-xl border-2 p-4 shadow-sm ${
        isEligible
          ? 'bg-success-50 border-success-500'
          : 'bg-white border-typography-900'
      }`}
    >
      <VStack space='md'>
        <HStack className='justify-between items-start'>
          <VStack className='flex-1' space='xs'>
            <Text size='md' className='text-typography-900 font-semibold'>
              {reward.title}
            </Text>
            <Text size='sm' className='text-typography-600'>
              {reward.description}
            </Text>
          </VStack>
          <VStack space='xs' className='items-end'>
            <Text size='sm' className='text-primary-500 font-bold'>
              {reward.pointsRequired} pts
            </Text>
            {isEligible ? (
              <Text size='xs' className='text-success-600 font-semibold'>
                ✓ Eligible
              </Text>
            ) : (
              <Text size='xs' className='text-error-600'>
                Need {pointsNeeded} more
              </Text>
            )}
          </VStack>
        </HStack>

        {/* Progress bar */}
        <VStack space='xs'>
          <HStack className='justify-between'>
            <Text size='xs' className='text-typography-600'>
              Your points: {customerPoints}
            </Text>
            <Text size='xs' className='text-typography-600'>
              Required: {reward.pointsRequired}
            </Text>
          </HStack>
          <Box className='h-2 bg-gray-200 rounded-full overflow-hidden'>
            <Box
              className={`h-full rounded-full ${
                isEligible ? 'bg-success-500' : 'bg-primary-500'
              }`}
              style={{
                width: `${Math.min((customerPoints / reward.pointsRequired) * 100, 100)}%`,
              }}
            />
          </Box>
        </VStack>
      </VStack>
    </Box>
  );
}

export function RewardsList({ data, loading = false }: RewardsListProps) {
  if (loading && data.length === 0) {
    return (
      <VStack space='md'>
        {[...Array(2)].map((_, index) => (
          <Box
            key={index}
            className='bg-white rounded-xl border-2 border-gray-200 p-4 shadow-sm'
          >
            <VStack space='md'>
              <HStack className='justify-between items-start'>
                <VStack className='flex-1' space='xs'>
                  <Box className='h-4 bg-gray-200 rounded animate-pulse' />
                  <Box className='h-3 bg-gray-200 rounded animate-pulse w-3/4' />
                </VStack>
                <Box className='w-16 h-4 bg-gray-200 rounded animate-pulse' />
              </HStack>
              <Box className='h-2 bg-gray-200 rounded animate-pulse' />
            </VStack>
          </Box>
        ))}
      </VStack>
    );
  }

  if (data.length === 0) {
    return (
      <Box className='bg-white rounded-xl border-2 border-typography-900 p-6 shadow-sm'>
        <VStack space='md' className='items-center'>
          <Text size='4xl'>🎁</Text>
          <Heading
            size='lg'
            className='text-typography-900 font-semibold text-center'
          >
            No rewards available
          </Heading>
          <Text size='md' className='text-typography-600 text-center'>
            This business hasn't set up any rewards yet. Check back later!
          </Text>
        </VStack>
      </Box>
    );
  }

  return (
    <FlatList
      data={data}
      keyExtractor={item => item.reward.id}
      renderItem={({ item }) => <RewardItem rewardEligibility={item} />}
      ItemSeparatorComponent={() => <Box className='h-3' />}
      showsVerticalScrollIndicator={false}
    />
  );
}
