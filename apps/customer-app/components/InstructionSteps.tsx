import { Box } from '@indie-points/ui-box';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

interface Step {
  number: number;
  title: string;
  description: string;
  color: string;
}

interface InstructionStepsProps {
  title: string;
  steps: Step[];
}

export function InstructionSteps({ title, steps }: InstructionStepsProps) {
  return (
    <VStack space='lg'>
      <Heading size='xl' className='text-typography-900 font-semibold'>
        {title}
      </Heading>

      {steps.map(step => (
        <HStack key={step.number} space='md' className='items-start'>
          <Box
            className={`w-8 h-8 ${step.color} rounded-full items-center justify-center border-2 border-black`}
          >
            <Text size='md' className='text-white font-bold'>
              {step.number}
            </Text>
          </Box>
          <VStack className='flex-1'>
            <Text size='md' className='text-typography-900 font-semibold'>
              {step.title}
            </Text>
            <Text size='sm' className='text-typography-600'>
              {step.description}
            </Text>
          </VStack>
        </HStack>
      ))}
    </VStack>
  );
}
