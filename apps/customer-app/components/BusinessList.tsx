import { Box } from '@indie-points/ui-box';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Pressable } from '@indie-points/ui-pressable';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';
import { FlatList } from 'react-native';

import { CustomerBusinessSummary } from '../services';

interface BusinessListProps {
  data: CustomerBusinessSummary[];
  loading?: boolean;
  onBusinessPress?: (business: CustomerBusinessSummary) => void;
}

interface BusinessItemProps {
  business: CustomerBusinessSummary;
  onPress?: (business: CustomerBusinessSummary) => void;
}

function BusinessItem({ business, onPress }: BusinessItemProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getCategoryIcon = (category: string) => {
    const categoryLower = category.toLowerCase();
    if (categoryLower.includes('restaurant') || categoryLower.includes('food')) return '🍽️';
    if (categoryLower.includes('coffee') || categoryLower.includes('cafe')) return '☕';
    if (categoryLower.includes('retail') || categoryLower.includes('shop')) return '🛍️';
    if (categoryLower.includes('service')) return '🔧';
    if (categoryLower.includes('health') || categoryLower.includes('fitness')) return '💪';
    return '🏪';
  };

  return (
    <Pressable
      onPress={() => onPress?.(business)}
      className='bg-white rounded-xl border-2 border-typography-900 p-4 shadow-sm'
    >
      <HStack space='md' className='items-center'>
        <Text size='2xl'>{getCategoryIcon(business.category)}</Text>
        <VStack className='flex-1' space='xs'>
          <HStack className='justify-between items-center'>
            <Text size='md' className='text-typography-900 font-semibold flex-1'>
              {business.name}
            </Text>
            <Text size='md' className='text-primary-500 font-bold'>
              {business.points} pts
            </Text>
          </HStack>
          <HStack className='justify-between items-center'>
            <Text size='sm' className='text-typography-600'>
              {business.category}
            </Text>
            <Text size='sm' className='text-typography-600'>
              Last visit: {formatDate(business.lastVisit)}
            </Text>
          </HStack>
        </VStack>
      </HStack>
    </Pressable>
  );
}

export function BusinessList({ data, loading = false, onBusinessPress }: BusinessListProps) {
  if (loading && data.length === 0) {
    return (
      <VStack space='md'>
        {[...Array(3)].map((_, index) => (
          <Box
            key={index}
            className='bg-white rounded-xl border-2 border-gray-200 p-4 shadow-sm'
          >
            <HStack space='md' className='items-center'>
              <Box className='w-8 h-8 bg-gray-200 rounded animate-pulse' />
              <VStack className='flex-1' space='xs'>
                <Box className='h-4 bg-gray-200 rounded animate-pulse' />
                <Box className='h-3 bg-gray-200 rounded animate-pulse w-3/4' />
              </VStack>
            </HStack>
          </Box>
        ))}
      </VStack>
    );
  }

  if (data.length === 0) {
    return (
      <Box className='bg-white rounded-xl border-2 border-typography-900 p-6 shadow-sm'>
        <VStack space='md' className='items-center'>
          <Text size='4xl'>🏪</Text>
          <Heading size='lg' className='text-typography-900 font-semibold text-center'>
            No businesses yet
          </Heading>
          <Text size='md' className='text-typography-600 text-center'>
            Visit participating businesses to start earning points and see them here.
          </Text>
        </VStack>
      </Box>
    );
  }

  return (
    <FlatList
      data={data}
      keyExtractor={(item) => item.id.toString()}
      renderItem={({ item }) => (
        <BusinessItem business={item} onPress={onBusinessPress} />
      )}
      ItemSeparatorComponent={() => <Box className='h-3' />}
      showsVerticalScrollIndicator={false}
    />
  );
}
